#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据迁移脚本
将分离的product_details和tier_prices表合并到统一表中
"""

import sqlite3
import os
from database_unified import UnifiedDatabaseManager


def check_source_database(db_path: str = "novoline_spider_v2.db"):
    """检查源数据库结构和数据"""
    if not os.path.exists(db_path):
        print(f"❌ 源数据库文件不存在: {db_path}")
        return False
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 检查表是否存在
            cursor.execute("""
                SELECT name FROM sqlite_master 
                WHERE type='table' AND name IN ('product_details', 'tier_prices')
            """)
            tables = [row[0] for row in cursor.fetchall()]
            
            if 'product_details' not in tables:
                print("❌ 源数据库中缺少 product_details 表")
                return False
            
            if 'tier_prices' not in tables:
                print("❌ 源数据库中缺少 tier_prices 表")
                return False
            
            # 检查数据量
            cursor.execute("SELECT COUNT(*) FROM product_details")
            product_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM tier_prices")
            tier_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM product_details WHERE crawl_status = 'success'")
            success_count = cursor.fetchone()[0]
            
            print(f"✅ 源数据库检查通过:")
            print(f"   📊 产品详情记录: {product_count}")
            print(f"   📊 阶梯价格记录: {tier_count}")
            print(f"   ✅ 成功采集的产品: {success_count}")
            
            return True
            
    except Exception as e:
        print(f"❌ 检查源数据库失败: {e}")
        return False


def preview_migration_data(db_path: str = "novoline_spider_v2.db", limit: int = 3):
    """预览迁移数据"""
    print(f"\n📋 预览迁移数据 (前{limit}个产品):")
    print("=" * 80)
    
    try:
        with sqlite3.connect(db_path) as conn:
            cursor = conn.cursor()
            
            # 获取前几个成功采集的产品
            cursor.execute('''
                SELECT 
                    pd.id, pd.product_url_id, pd.title, pd.sku, pd.nav_full,
                    pd.regular_price, pd.crawl_status
                FROM product_details pd
                WHERE pd.crawl_status = 'success'
                ORDER BY pd.id
                LIMIT ?
            ''', (limit,))
            
            products = cursor.fetchall()
            
            for i, product in enumerate(products, 1):
                (pd_id, product_url_id, title, sku, nav_full, regular_price, crawl_status) = product
                
                print(f"\n{i}. 产品信息:")
                print(f"   ID: {pd_id} | URL_ID: {product_url_id}")
                print(f"   标题: {title}")
                print(f"   SKU: {sku}")
                print(f"   导航: {nav_full}")
                print(f"   基础价格: ${regular_price}")
                print(f"   状态: {crawl_status}")
                
                # 获取该产品的阶梯价格
                cursor.execute('''
                    SELECT 
                        quantity_min, quantity_max, quantity_display, 
                        unit_price, discount_percent
                    FROM tier_prices
                    WHERE product_detail_id = ?
                    ORDER BY quantity_min
                ''', (pd_id,))
                
                tiers = cursor.fetchall()
                
                if tiers:
                    print(f"   📊 阶梯价格 ({len(tiers)}层):")
                    for j, tier in enumerate(tiers, 1):
                        (qty_min, qty_max, qty_display, unit_price, discount) = tier
                        print(f"      {j}. {qty_display} - ${unit_price} ({discount}% off)")
                else:
                    print(f"   📊 阶梯价格: 无")
                
                print(f"   → 将生成 {len(tiers) if tiers else 1} 条统一记录")
            
    except Exception as e:
        print(f"❌ 预览数据失败: {e}")


def perform_migration():
    """执行数据迁移"""
    print(f"\n🔄 开始数据迁移...")
    print("=" * 50)
    
    # 创建统一数据库管理器
    unified_db = UnifiedDatabaseManager()
    
    # 执行迁移
    success = unified_db.migrate_from_separate_tables()
    
    if success:
        # 显示迁移结果统计
        stats = unified_db.get_statistics()
        
        print(f"\n📊 迁移结果统计:")
        print(f"   总产品数: {stats['total_products']}")
        print(f"   总记录数: {stats['total_records']}")
        print(f"   最大价格层级: {stats['max_price_tiers']}")
        print(f"   状态分布: {stats['status_breakdown']}")
        
        # 显示示例数据
        print(f"\n📋 统一表示例数据:")
        sample_data = unified_db.get_unified_products(limit=3)
        
        for i, row in enumerate(sample_data, 1):
            (id, product_url_id, title, sku, nav_level1, nav_level2, nav_level3, nav_full,
             category_name, category_url, regular_price, sale_price, currency,
             tier_quantity_min, tier_quantity_max, tier_quantity_display,
             tier_unit_price, tier_total_price, tier_discount_percent, tier_discount_amount,
             crawl_status, error_message, created_at) = row
            
            print(f"\n   {i}. ID:{id} | 产品:{title}")
            print(f"      SKU: {sku} | 导航: {nav_full}")
            print(f"      基础价格: ${regular_price} | 层级: {tier_quantity_display}")
            print(f"      单价: ${tier_unit_price} | 折扣: {tier_discount_percent}%")
        
        return True
    else:
        return False


def main():
    """主函数"""
    print("🔄 Novoline Spider 数据迁移工具")
    print("=" * 50)
    print("功能: 将分离的产品详情和阶梯价格表合并为统一表")
    print("=" * 50)
    
    # 1. 检查源数据库
    if not check_source_database():
        print("\n❌ 源数据库检查失败，无法继续迁移")
        return
    
    # 2. 预览迁移数据
    preview_migration_data()
    
    # 3. 确认迁移
    print(f"\n❓ 确认执行数据迁移？")
    print("   这将创建新的统一数据库文件: novoline_spider_unified.db")
    
    confirm = input("   输入 'yes' 确认迁移: ").strip().lower()
    
    if confirm != 'yes':
        print("❌ 迁移已取消")
        return
    
    # 4. 执行迁移
    if perform_migration():
        print(f"\n✅ 数据迁移完成！")
        print(f"   新的统一数据库: novoline_spider_unified.db")
        print(f"   可以使用新的GUI界面查看合并后的数据")
    else:
        print(f"\n❌ 数据迁移失败")


if __name__ == "__main__":
    main()
