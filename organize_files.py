#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动整理项目文件
"""

import os
import shutil

def organize_files():
    """整理文件到对应目录"""
    
    # 创建目录
    dirs = ['test', 'demo', 'backup', 'docs']
    for d in dirs:
        if not os.path.exists(d):
            os.makedirs(d)
            print(f"创建目录: {d}")
    
    # 移动文件
    moves = [
        # 测试文件到test目录
        ('test_*.py', 'test'),
        ('test_*.db', 'test'),
        ('quick_test*.py', 'test'),
        ('check_crawl_status.py', 'test'),
        
        # 演示文件到demo目录
        ('demo_*.py', 'demo'),
        ('demo_*.db', 'demo'),
        
        # 备份文件到backup目录
        ('*.backup_*', 'backup'),
        
        # 文档到docs目录
        ('*.md', 'docs'),
        ('*.txt', 'docs'),
    ]
    
    import glob
    for pattern, target in moves:
        files = glob.glob(pattern)
        for f in files:
            if os.path.isfile(f):
                target_path = os.path.join(target, f)
                if os.path.exists(target_path):
                    os.remove(target_path)
                shutil.move(f, target_path)
                print(f"移动: {f} -> {target_path}")

if __name__ == "__main__":
    organize_files()
    print("文件整理完成！")
