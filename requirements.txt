# Novoline Spider Project Dependencies
# Core web scraping dependencies
requests>=2.32.0
beautifulsoup4>=4.12.0
lxml>=5.2.0

# GUI dependencies (tkinter is built-in Python library)
# tkinter - Python standard library

# Database dependencies (sqlite3 is built-in Python library)
# sqlite3 - Python standard library

# Type hints support
typing-extensions>=4.11.0

# Development and testing dependencies (optional)
# pytest>=7.0.0
# pytest-cov>=4.0.0
