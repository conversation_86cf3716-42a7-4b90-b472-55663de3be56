#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据统计查看工具
用于查看爬虫收集的数据统计信息，便于核对数量
"""

import os
import sys
import sqlite3
from database import DatabaseManager

def show_detailed_stats(db_path):
    """显示详细统计信息"""
    if not os.path.exists(db_path):
        print(f"❌ 数据库文件不存在: {db_path}")
        return
    
    db = DatabaseManager(db_path)
    
    print("=" * 80)
    print(f"数据库统计信息: {db_path}")
    print("=" * 80)
    
    # 基础统计
    basic_stats = db.get_statistics()
    print("📊 基础统计:")
    print(f"   主目录数量: {basic_stats['main_categories']}")
    print(f"   子目录数量: {basic_stats['sub_categories']}")
    print(f"   产品数量: {basic_stats['products']}")
    print(f"   价格记录: {basic_stats['tier_prices']}")
    print(f"   错误记录: {basic_stats['errors']}")
    print()
    
    # 分类详细统计
    category_stats = db.get_category_stats()
    
    print("📋 分类详细统计:")
    print("-" * 80)
    
    main_totals = {}
    grand_total = 0
    
    for stat in category_stats:
        if stat['sub_name']:
            main_name = stat['main_name']
            sub_name = stat['sub_name']
            product_count = stat['actual_product_count']
            page_count = stat['page_count']
            recorded_count = stat['recorded_product_count']
            
            # 累计主目录统计
            if main_name not in main_totals:
                main_totals[main_name] = {
                    'products': 0, 
                    'subcategories': 0,
                    'pages': 0
                }
            main_totals[main_name]['products'] += product_count
            main_totals[main_name]['subcategories'] += 1
            main_totals[main_name]['pages'] += page_count
            grand_total += product_count
            
            # 显示子目录信息
            status = "✅" if product_count > 0 else "⏳"
            consistency = "✓" if product_count == recorded_count else "⚠"
            
            print(f"{status} {main_name} > {sub_name}")
            print(f"    产品数量: {product_count} {consistency}")
            if page_count > 0:
                print(f"    页面数量: {page_count}")
                avg_per_page = product_count / page_count if page_count > 0 else 0
                print(f"    平均每页: {avg_per_page:.1f} 个产品")
            if stat['last_crawled_at']:
                print(f"    最后爬取: {stat['last_crawled_at']}")
            print()
    
    print("📈 主目录汇总:")
    print("-" * 50)
    for main_name, totals in main_totals.items():
        avg_per_subcat = totals['products'] / totals['subcategories'] if totals['subcategories'] > 0 else 0
        print(f"{main_name}:")
        print(f"  产品总数: {totals['products']}")
        print(f"  子目录数: {totals['subcategories']}")
        print(f"  总页数: {totals['pages']}")
        print(f"  平均每子目录: {avg_per_subcat:.1f} 个产品")
        print()
    
    print(f"🎯 总计: {grand_total} 个产品")
    print("=" * 80)

def show_progress_info(db_path):
    """显示爬取进度信息"""
    if not os.path.exists(db_path):
        return
    
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT stage, status, total_items, completed_items, 
                   started_at, completed_at, error_message
            FROM crawl_progress
            ORDER BY id DESC
        ''')
        
        progress_records = cursor.fetchall()
    
    if progress_records:
        print("\n📈 爬取进度记录:")
        print("-" * 60)
        
        for record in progress_records:
            stage, status, total, completed, started, finished, error = record
            
            status_icon = {
                'pending': '⏳',
                'in_progress': '🔄',
                'completed': '✅',
                'failed': '❌'
            }.get(status, '❓')
            
            print(f"{status_icon} {stage.upper()}: {status}")
            if total and completed:
                percentage = (completed / total) * 100 if total > 0 else 0
                print(f"   进度: {completed}/{total} ({percentage:.1f}%)")
            if started:
                print(f"   开始时间: {started}")
            if finished:
                print(f"   完成时间: {finished}")
            if error:
                print(f"   错误信息: {error}")
            print()

def show_sample_products(db_path, limit=5):
    """显示样本产品"""
    if not os.path.exists(db_path):
        return
    
    with sqlite3.connect(db_path) as conn:
        cursor = conn.cursor()
        cursor.execute('''
            SELECT 
                mc.category_name,
                sc.sub_category_name,
                pu.product_name,
                pu.product_sku,
                pu.product_price,
                pu.product_url
            FROM product_urls pu
            JOIN sub_categories sc ON pu.sub_category_id = sc.id
            JOIN main_categories mc ON sc.main_category_id = mc.id
            ORDER BY pu.id
            LIMIT ?
        ''', (limit,))
        
        products = cursor.fetchall()
    
    if products:
        print(f"\n📦 样本产品 (前{limit}个):")
        print("-" * 80)
        
        for i, (main_cat, sub_cat, name, sku, price, url) in enumerate(products, 1):
            print(f"{i:2d}. {name}")
            print(f"     分类: {main_cat} > {sub_cat}")
            print(f"     SKU: {sku or 'N/A'}")
            print(f"     价格: {price or 'N/A'}")
            print(f"     URL: {url}")
            print()

def main():
    """主函数"""
    print("Novoline爬虫数据统计查看工具")
    print("=" * 50)
    
    # 查找可用的数据库文件
    db_files = []
    for file in os.listdir('.'):
        if file.endswith('.db'):
            db_files.append(file)
    
    if not db_files:
        print("❌ 当前目录下没有找到数据库文件")
        return
    
    print("📁 找到以下数据库文件:")
    for i, db_file in enumerate(db_files, 1):
        file_size = os.path.getsize(db_file) / 1024  # KB
        print(f"   {i}. {db_file} ({file_size:.1f} KB)")
    
    # 选择数据库文件
    if len(db_files) == 1:
        selected_db = db_files[0]
        print(f"\n自动选择: {selected_db}")
    else:
        try:
            choice = input(f"\n请选择数据库文件 (1-{len(db_files)}): ").strip()
            index = int(choice) - 1
            if 0 <= index < len(db_files):
                selected_db = db_files[index]
            else:
                print("❌ 无效选择")
                return
        except ValueError:
            print("❌ 请输入有效数字")
            return
    
    # 显示统计信息
    show_detailed_stats(selected_db)
    show_progress_info(selected_db)
    
    # 询问是否查看样本产品
    choice = input("\n是否查看样本产品？(y/n): ").lower().strip()
    if choice == 'y':
        try:
            limit = input("显示多少个产品？(默认5个): ").strip()
            limit = int(limit) if limit else 5
            show_sample_products(selected_db, limit)
        except ValueError:
            show_sample_products(selected_db, 5)

if __name__ == "__main__":
    main()
