#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一数据库测试脚本
测试数据迁移和统一表功能
"""

import os
import sqlite3
from database_unified import UnifiedDatabaseManager


def test_unified_database():
    """测试统一数据库功能"""
    print("🧪 统一数据库功能测试")
    print("=" * 50)
    
    # 1. 检查源数据库
    source_db = "novoline_spider_v2.db"
    if not os.path.exists(source_db):
        print(f"❌ 源数据库不存在: {source_db}")
        print("请先运行第三阶段采集，生成源数据")
        return False
    
    print(f"✅ 源数据库存在: {source_db}")
    
    # 2. 检查源数据库内容
    with sqlite3.connect(source_db) as conn:
        cursor = conn.cursor()
        
        # 检查产品详情
        cursor.execute("SELECT COUNT(*) FROM product_details WHERE crawl_status = 'success'")
        success_products = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM tier_prices")
        tier_count = cursor.fetchone()[0]
        
        print(f"📊 源数据统计:")
        print(f"   成功采集的产品: {success_products}")
        print(f"   阶梯价格记录: {tier_count}")
        
        if success_products == 0:
            print("❌ 没有成功采集的产品数据，无法测试迁移")
            return False
    
    # 3. 创建统一数据库
    print(f"\n🔄 创建统一数据库...")
    unified_db = UnifiedDatabaseManager("test_unified.db")
    
    # 4. 执行数据迁移
    print(f"🔄 执行数据迁移...")
    success = unified_db.migrate_from_separate_tables(source_db)
    
    if not success:
        print("❌ 数据迁移失败")
        return False
    
    print("✅ 数据迁移成功")
    
    # 5. 验证迁移结果
    print(f"\n📊 验证迁移结果...")
    stats = unified_db.get_statistics()
    
    print(f"统计信息:")
    print(f"   总产品数: {stats['total_products']}")
    print(f"   总记录数: {stats['total_records']}")
    print(f"   最大价格层级: {stats['max_price_tiers']}")
    print(f"   状态分布: {stats['status_breakdown']}")
    
    # 6. 显示示例数据
    print(f"\n📋 统一表示例数据:")
    sample_data = unified_db.get_unified_products(limit=5)
    
    print(f"{'ID':<5} {'产品ID':<8} {'标题':<30} {'SKU':<15} {'数量':<15} {'单价':<10} {'折扣':<8}")
    print("-" * 100)
    
    for row in sample_data:
        (id, product_url_id, title, sku, nav_level1, nav_level2, nav_level3, nav_full,
         category_name, category_url, regular_price, sale_price, currency,
         tier_quantity_min, tier_quantity_max, tier_quantity_display,
         tier_unit_price, tier_total_price, tier_discount_percent, tier_discount_amount,
         crawl_status, error_message, created_at) = row
        
        title_short = (title[:27] + "...") if title and len(title) > 30 else (title or "NULL")
        sku_short = sku or "NULL"
        qty_display = tier_quantity_display or "NULL"
        unit_price = f"${tier_unit_price}" if tier_unit_price else "NULL"
        discount = f"{tier_discount_percent}%" if tier_discount_percent else "NULL"
        
        print(f"{id:<5} {product_url_id:<8} {title_short:<30} {sku_short:<15} {qty_display:<15} {unit_price:<10} {discount:<8}")
    
    # 7. 测试查询功能
    print(f"\n🔍 测试查询功能...")
    
    # 按状态过滤
    success_data = unified_db.get_unified_products(limit=10, filter_status='success')
    print(f"   成功状态记录: {len(success_data)}")
    
    # 8. 对比原始数据
    print(f"\n🔍 数据完整性验证...")
    
    with sqlite3.connect(source_db) as source_conn:
        source_cursor = source_conn.cursor()
        
        # 统计原始数据
        source_cursor.execute('''
            SELECT COUNT(*) FROM product_details pd
            JOIN tier_prices tp ON pd.id = tp.product_detail_id
            WHERE pd.crawl_status = 'success'
        ''')
        original_tier_records = source_cursor.fetchone()[0]
        
        source_cursor.execute('''
            SELECT COUNT(*) FROM product_details
            WHERE crawl_status = 'success'
        ''')
        original_products = source_cursor.fetchone()[0]
    
    print(f"   原始阶梯价格记录: {original_tier_records}")
    print(f"   迁移后记录数: {stats['total_records']}")
    print(f"   原始产品数: {original_products}")
    print(f"   迁移后产品数: {stats['total_products']}")
    
    # 验证数据一致性
    if stats['total_products'] == original_products:
        print("✅ 产品数量一致")
    else:
        print("❌ 产品数量不一致")
    
    # 9. 清理测试文件
    print(f"\n🧹 清理测试文件...")
    try:
        if os.path.exists("test_unified.db"):
            os.remove("test_unified.db")
        print("✅ 测试文件已清理")
    except Exception as e:
        print(f"⚠️ 清理测试文件失败: {e}")
    
    print(f"\n✅ 统一数据库测试完成")
    return True


def show_comparison():
    """显示新旧数据结构对比"""
    print("\n📊 数据结构对比")
    print("=" * 80)
    
    print("🔴 原始结构 (分离表):")
    print("   product_details 表:")
    print("   ├── id, product_url_id, title, sku")
    print("   ├── nav_level1, nav_level2, nav_level3, nav_full")
    print("   ├── category_name, regular_price, sale_price")
    print("   └── crawl_status, error_message, created_at")
    print("")
    print("   tier_prices 表:")
    print("   ├── id, product_detail_id")
    print("   ├── quantity_min, quantity_max, quantity_display")
    print("   ├── unit_price, discount_percent")
    print("   └── raw_data, created_at")
    print("")
    print("   问题: 需要JOIN查询，数据分散，查看不便")
    
    print("\n🟢 统一结构 (合并表):")
    print("   unified_products 表:")
    print("   ├── 基本信息: id, product_url_id, title, sku")
    print("   ├── 导航信息: nav_level1, nav_level2, nav_level3, nav_full")
    print("   ├── 分类信息: category_name, category_url")
    print("   ├── 基础价格: regular_price, sale_price, currency")
    print("   ├── 阶梯价格: tier_quantity_*, tier_unit_price, tier_discount_*")
    print("   ├── 状态信息: crawl_status, error_message")
    print("   └── 时间信息: created_at, updated_at")
    print("")
    print("   优势: 一张表包含所有信息，查看方便，分析简单")
    
    print("\n📈 数据展示对比:")
    print("   原始方式: 产品信息和价格信息分开显示，需要切换Tab")
    print("   统一方式: 所有信息在一个表格中，每行包含完整的产品+价格信息")


def main():
    """主函数"""
    print("🧪 Novoline Spider 统一数据库测试")
    print("=" * 50)
    
    # 显示对比说明
    show_comparison()
    
    # 执行测试
    if test_unified_database():
        print(f"\n🎉 测试通过！可以使用统一数据库功能")
        print(f"💡 使用方法:")
        print(f"   1. 运行 python migrate_to_unified.py 进行数据迁移")
        print(f"   2. 在GUI中查看 '统一数据' Tab页")
        print(f"   3. 享受更便捷的数据查看体验")
    else:
        print(f"\n❌ 测试失败，请检查源数据")


if __name__ == "__main__":
    main()
